"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CpuChipIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CpuChipIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CpuChipIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CpuChipIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Smart AI Routing\",\n        subtitle: \"when you need it\",\n        description: \"RouKey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.\",\n        details: [\n            \"Write JavaScript or Python - you can always fall back to code\",\n            \"Add libraries from npm or Python for even more power\",\n            \"Paste cURL requests into your workflow\",\n            \"Merge workflow branches, don't just split them\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Enterprise Security\",\n        subtitle: \"when you don't\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        details: [\n            \"Re-run single steps without re-running the whole workflow\",\n            \"Replay or mock data to avoid waiting for external systems\",\n            \"Debug fast with logs in line with your code\",\n            \"Use 1700+ templates to jump-start your project\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Cost Optimization\",\n        subtitle: \"Repeat\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        details: [\n            \"Re-run single steps without re-running the whole workflow\",\n            \"Replay or mock data to avoid waiting for external systems\",\n            \"Debug fast with logs in line with your code\",\n            \"Use 1700+ templates to jump-start your project\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"300+ AI Models\",\n        subtitle: \"unified access\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        details: [\n            \"Connect to any AI provider with one API\",\n            \"Automatic failover and load balancing\",\n            \"Real-time performance monitoring\",\n            \"Cost tracking across all providers\"\n        ]\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    const [scrollProgress, setScrollProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const accumulatedDelta = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const isInSection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const handleScroll = {\n                \"FeaturesSection.useEffect.handleScroll\": (e)=>{\n                    if (!sectionRef.current || !containerRef.current) return;\n                    const rect = sectionRef.current.getBoundingClientRect();\n                    const sectionTop = rect.top;\n                    const sectionBottom = rect.bottom;\n                    const windowHeight = window.innerHeight;\n                    // Check if we're in the section\n                    isInSection.current = sectionTop <= windowHeight * 0.5 && sectionBottom >= windowHeight * 0.5;\n                    if (!isInSection.current) return;\n                    // Prevent default scrolling when in section\n                    e.preventDefault();\n                    // Accumulate scroll delta for smoother control\n                    accumulatedDelta.current += e.deltaY * 0.5;\n                    // Calculate progress through all cards (0 to features.length - 1)\n                    const maxProgress = (features.length - 1) * 100;\n                    const newProgress = Math.max(0, Math.min(maxProgress, accumulatedDelta.current));\n                    setScrollProgress(newProgress);\n                }\n            }[\"FeaturesSection.useEffect.handleScroll\"];\n            const handleWheel = {\n                \"FeaturesSection.useEffect.handleWheel\": (e)=>handleScroll(e)\n            }[\"FeaturesSection.useEffect.handleWheel\"];\n            window.addEventListener('wheel', handleWheel, {\n                passive: false\n            });\n            return ({\n                \"FeaturesSection.useEffect\": ()=>window.removeEventListener('wheel', handleWheel)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"relative overflow-hidden\",\n        ref: sectionRef,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gradient-to-br from-[#040716] to-[#1C051C] relative min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    gridSize: 45,\n                    opacity: 0.06,\n                    color: \"#ff6b35\",\n                    variant: \"premium\",\n                    animated: true,\n                    className: \"absolute inset-0\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center pt-20 pb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 15\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    className: \"text-4xl sm:text-5xl font-bold text-white mb-6 leading-tight\",\n                                    children: [\n                                        \"Enterprise-Grade\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                            children: [\n                                                ' ',\n                                                \"AI Infrastructure\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 15\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.3,\n                                        delay: 0.05\n                                    },\n                                    className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                                    children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: containerRef,\n                            className: \"relative h-[600px] overflow-hidden\",\n                            children: features.map((feature, index)=>{\n                                // Calculate this card's position based on scroll progress\n                                const cardProgress = scrollProgress - index * 100;\n                                const translateY = Math.max(-100, Math.min(100, cardProgress));\n                                const opacity = Math.max(0, Math.min(1, 1 - Math.abs(cardProgress) / 100));\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center transition-none\",\n                                    style: {\n                                        transform: \"translateY(\".concat(translateY, \"%)\"),\n                                        opacity: opacity\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full max-w-6xl mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-900/60 to-gray-800/40 backdrop-blur-sm border border-gray-700/50 rounded-3xl p-12 hover:border-[#ff6b35]/30 transition-colors duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mb-8\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                            className: \"h-8 w-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 179,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 178,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                                                children: feature.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 182,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-lg text-gray-400\",\n                                                                                children: feature.subtitle\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 185,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 181,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                                children: feature.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: feature.details.map((detail, detailIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-6 h-6 bg-[#ff6b35]/20 rounded-lg flex items-center justify-center mt-0.5\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 197,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 196,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-300 leading-relaxed\",\n                                                                                children: detail\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 199,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, detailIndex, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 195,\n                                                                        columnNumber: 31\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-2xl p-8 border border-gray-700/30\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"aspect-[4/3] bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                        className: \"h-16 w-16 text-[#ff6b35] mx-auto mb-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Interactive demo coming soon\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 210,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 19\n                                    }, this)\n                                }, feature.title, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mt-12 space-x-3\",\n                            children: features.map((_, index)=>{\n                                const currentCardIndex = Math.round(scrollProgress / 100);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        accumulatedDelta.current = index * 100;\n                                        setScrollProgress(index * 100);\n                                    },\n                                    className: \"w-3 h-3 rounded-full transition-all duration-300 \".concat(index === currentCardIndex ? 'bg-[#ff6b35] scale-125' : 'bg-gray-600 hover:bg-gray-500')\n                                }, index, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n_s(FeaturesSection, \"LELuoSyFyBl6X/XwKtILP5Szfco=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});