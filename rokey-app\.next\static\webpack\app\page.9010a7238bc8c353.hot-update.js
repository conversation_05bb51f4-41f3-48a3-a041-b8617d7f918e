"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CpuChipIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CpuChipIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CpuChipIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CpuChipIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Smart AI Routing\",\n        subtitle: \"when you need it\",\n        description: \"RouKey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.\",\n        details: [\n            \"Write JavaScript or Python - you can always fall back to code\",\n            \"Add libraries from npm or Python for even more power\",\n            \"Paste cURL requests into your workflow\",\n            \"Merge workflow branches, don't just split them\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Enterprise Security\",\n        subtitle: \"when you don't\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        details: [\n            \"Re-run single steps without re-running the whole workflow\",\n            \"Replay or mock data to avoid waiting for external systems\",\n            \"Debug fast with logs in line with your code\",\n            \"Use 1700+ templates to jump-start your project\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Cost Optimization\",\n        subtitle: \"Repeat\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        details: [\n            \"Re-run single steps without re-running the whole workflow\",\n            \"Replay or mock data to avoid waiting for external systems\",\n            \"Debug fast with logs in line with your code\",\n            \"Use 1700+ templates to jump-start your project\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CpuChipIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"300+ AI Models\",\n        subtitle: \"unified access\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        details: [\n            \"Connect to any AI provider with one API\",\n            \"Automatic failover and load balancing\",\n            \"Real-time performance monitoring\",\n            \"Cost tracking across all providers\"\n        ]\n    }\n];\nfunction FeaturesSection() {\n    _s();\n    const [currentCard, setCurrentCard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isScrolling, setIsScrolling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cardRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeaturesSection.useEffect\": ()=>{\n            const handleScroll = {\n                \"FeaturesSection.useEffect.handleScroll\": (e)=>{\n                    if (!sectionRef.current) return;\n                    const rect = sectionRef.current.getBoundingClientRect();\n                    const isInSection = rect.top <= window.innerHeight && rect.bottom >= 0;\n                    if (!isInSection || isScrolling) return;\n                    // Check if we're at the beginning or end of the card sequence\n                    if (e.deltaY > 0 && currentCard < features.length - 1) {\n                        // Scrolling down and not at last card\n                        e.preventDefault();\n                        setIsScrolling(true);\n                        setCurrentCard({\n                            \"FeaturesSection.useEffect.handleScroll\": (prev)=>prev + 1\n                        }[\"FeaturesSection.useEffect.handleScroll\"]);\n                        setTimeout({\n                            \"FeaturesSection.useEffect.handleScroll\": ()=>setIsScrolling(false)\n                        }[\"FeaturesSection.useEffect.handleScroll\"], 800);\n                    } else if (e.deltaY < 0 && currentCard > 0) {\n                        // Scrolling up and not at first card\n                        e.preventDefault();\n                        setIsScrolling(true);\n                        setCurrentCard({\n                            \"FeaturesSection.useEffect.handleScroll\": (prev)=>prev - 1\n                        }[\"FeaturesSection.useEffect.handleScroll\"]);\n                        setTimeout({\n                            \"FeaturesSection.useEffect.handleScroll\": ()=>setIsScrolling(false)\n                        }[\"FeaturesSection.useEffect.handleScroll\"], 800);\n                    }\n                }\n            }[\"FeaturesSection.useEffect.handleScroll\"];\n            window.addEventListener('wheel', handleScroll, {\n                passive: false\n            });\n            return ({\n                \"FeaturesSection.useEffect\": ()=>window.removeEventListener('wheel', handleScroll)\n            })[\"FeaturesSection.useEffect\"];\n        }\n    }[\"FeaturesSection.useEffect\"], [\n        currentCard,\n        isScrolling\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"relative overflow-hidden\",\n        ref: sectionRef,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gradient-to-br from-[#040716] to-[#1C051C] relative min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    gridSize: 45,\n                    opacity: 0.06,\n                    color: \"#ff6b35\",\n                    variant: \"premium\",\n                    animated: true,\n                    className: \"absolute inset-0\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center pt-20 pb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 15\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    className: \"text-4xl sm:text-5xl font-bold text-white mb-6 leading-tight\",\n                                    children: [\n                                        \"Enterprise-Grade\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                            children: [\n                                                ' ',\n                                                \"AI Infrastructure\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 15\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        duration: 0.3,\n                                        delay: 0.05\n                                    },\n                                    className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                                    children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative h-[600px] overflow-hidden\",\n                            children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    ref: (el)=>cardRefs.current[index] = el,\n                                    initial: {\n                                        y: index === 0 ? 0 : 100,\n                                        opacity: index === 0 ? 1 : 0\n                                    },\n                                    animate: {\n                                        y: index === currentCard ? 0 : index < currentCard ? -100 : 100,\n                                        opacity: index === currentCard ? 1 : 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        ease: \"easeInOut\"\n                                    },\n                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full max-w-6xl mx-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-900/60 to-gray-800/40 backdrop-blur-sm border border-gray-700/50 rounded-3xl p-12 hover:border-[#ff6b35]/30 transition-all duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mb-8\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                            className: \"h-8 w-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                            lineNumber: 168,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 167,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                                                children: feature.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 171,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-lg text-gray-400\",\n                                                                                children: feature.subtitle\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 174,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                                                children: feature.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: feature.details.map((detail, detailIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-6 h-6 bg-[#ff6b35]/20 rounded-lg flex items-center justify-center mt-0.5\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                    lineNumber: 186,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 185,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-300 leading-relaxed\",\n                                                                                children: detail\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                                lineNumber: 188,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, detailIndex, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 184,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-2xl p-8 border border-gray-700/30\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"aspect-[4/3] bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                        className: \"h-16 w-16 text-[#ff6b35] mx-auto mb-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 198,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: \"Interactive demo coming soon\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                        lineNumber: 199,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                }, feature.title, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mt-12 space-x-3\",\n                            children: features.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentCard(index),\n                                    className: \"w-3 h-3 rounded-full transition-all duration-300 \".concat(index === currentCard ? 'bg-[#ff6b35] scale-125' : 'bg-gray-600 hover:bg-gray-500')\n                                }, index, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n_s(FeaturesSection, \"IRTqoAaXrcMS2m9cmBpqXqV7WJk=\");\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});