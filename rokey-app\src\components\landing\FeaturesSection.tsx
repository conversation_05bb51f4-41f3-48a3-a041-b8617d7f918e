'use client';

import { motion } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';
import EnhancedGridBackground from './EnhancedGridBackground';
import {
  BoltIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CpuChipIcon,
  ClockIcon,
  CurrencyDollarIcon,
  Cog6ToothIcon,
  NoSymbolIcon,
  CodeBracketIcon,
  GlobeAltIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    icon: BoltIcon,
    title: "Smart AI Routing",
    subtitle: "when you need it",
    description: "Rou<PERSON>ey automatically detects your request type and routes it to the optimal AI model. No manual switching between providers.",
    details: [
      "Write JavaScript or Python - you can always fall back to code",
      "Add libraries from npm or Python for even more power",
      "Paste cURL requests into your workflow",
      "Merge workflow branches, don't just split them"
    ]
  },
  {
    icon: ShieldCheckIcon,
    title: "Enterprise Security",
    subtitle: "when you don't",
    description: "Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.",
    details: [
      "Re-run single steps without re-running the whole workflow",
      "Replay or mock data to avoid waiting for external systems",
      "Debug fast with logs in line with your code",
      "Use 1700+ templates to jump-start your project"
    ]
  },
  {
    icon: ChartBarIcon,
    title: "Cost Optimization",
    subtitle: "Repeat",
    description: "Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.",
    details: [
      "Re-run single steps without re-running the whole workflow",
      "Replay or mock data to avoid waiting for external systems",
      "Debug fast with logs in line with your code",
      "Use 1700+ templates to jump-start your project"
    ]
  },
  {
    icon: CpuChipIcon,
    title: "300+ AI Models",
    subtitle: "unified access",
    description: "Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.",
    details: [
      "Connect to any AI provider with one API",
      "Automatic failover and load balancing",
      "Real-time performance monitoring",
      "Cost tracking across all providers"
    ]
  }
];

export default function FeaturesSection() {
  const [currentCard, setCurrentCard] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    const handleScroll = (e: WheelEvent) => {
      if (!sectionRef.current) return;

      const rect = sectionRef.current.getBoundingClientRect();
      const isInSection = rect.top <= window.innerHeight && rect.bottom >= 0;

      if (!isInSection || isScrolling) return;

      // Check if we're at the beginning or end of the card sequence
      if (e.deltaY > 0 && currentCard < features.length - 1) {
        // Scrolling down and not at last card
        e.preventDefault();
        setIsScrolling(true);
        setCurrentCard(prev => prev + 1);
        setTimeout(() => setIsScrolling(false), 800);
      } else if (e.deltaY < 0 && currentCard > 0) {
        // Scrolling up and not at first card
        e.preventDefault();
        setIsScrolling(true);
        setCurrentCard(prev => prev - 1);
        setTimeout(() => setIsScrolling(false), 800);
      }
    };

    window.addEventListener('wheel', handleScroll, { passive: false });
    return () => window.removeEventListener('wheel', handleScroll);
  }, [currentCard, isScrolling]);

  return (
    <section id="features" className="relative overflow-hidden" ref={sectionRef}>
      {/* Background with RouKey colors */}
      <div className="bg-gradient-to-br from-[#040716] to-[#1C051C] relative min-h-screen">
        {/* Enhanced Grid Background */}
        <EnhancedGridBackground
          gridSize={45}
          opacity={0.06}
          color="#ff6b35"
          variant="premium"
          animated={true}
          className="absolute inset-0"
        />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          {/* Section Header */}
          <div className="text-center pt-20 pb-16">
            <motion.h2
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3 }}
              className="text-4xl sm:text-5xl font-bold text-white mb-6 leading-tight"
            >
              Enterprise-Grade
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
                {' '}AI Infrastructure
              </span>
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 15 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.05 }}
              className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
            >
              RouKey provides military-grade security, intelligent routing, and comprehensive analytics
              for the most demanding AI workloads. Built for scale, designed for performance.
            </motion.p>
          </div>

          {/* Scrolling Cards Container */}
          <div className="relative h-[600px] overflow-hidden">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                ref={el => cardRefs.current[index] = el}
                initial={{ y: index === 0 ? 0 : 100, opacity: index === 0 ? 1 : 0 }}
                animate={{
                  y: index === currentCard ? 0 : index < currentCard ? -100 : 100,
                  opacity: index === currentCard ? 1 : 0
                }}
                transition={{ duration: 0.8, ease: "easeInOut" }}
                className="absolute inset-0 flex items-center justify-center"
              >
                <div className="w-full max-w-6xl mx-auto">
                  <div className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 backdrop-blur-sm border border-gray-700/50 rounded-3xl p-12 hover:border-[#ff6b35]/30 transition-all duration-300">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                      {/* Left Side - Content */}
                      <div>
                        <div className="flex items-center gap-4 mb-8">
                          <div className="w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center">
                            <feature.icon className="h-8 w-8 text-white" />
                          </div>
                          <div>
                            <h3 className="text-3xl font-bold text-white mb-2">
                              {feature.title}
                            </h3>
                            <p className="text-lg text-gray-400">{feature.subtitle}</p>
                          </div>
                        </div>

                        <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                          {feature.description}
                        </p>

                        <div className="space-y-4">
                          {feature.details.map((detail, detailIndex) => (
                            <div key={detailIndex} className="flex items-start gap-3">
                              <div className="w-6 h-6 bg-[#ff6b35]/20 rounded-lg flex items-center justify-center mt-0.5">
                                <div className="w-2 h-2 bg-[#ff6b35] rounded-full"></div>
                              </div>
                              <span className="text-gray-300 leading-relaxed">{detail}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Right Side - Visual/Demo Area */}
                      <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-2xl p-8 border border-gray-700/30">
                        <div className="aspect-[4/3] bg-gradient-to-br from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl flex items-center justify-center">
                          <div className="text-center">
                            <feature.icon className="h-16 w-16 text-[#ff6b35] mx-auto mb-4" />
                            <p className="text-gray-400">Interactive demo coming soon</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Card Indicators */}
          <div className="flex justify-center mt-12 space-x-3">
            {features.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentCard(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentCard
                    ? 'bg-[#ff6b35] scale-125'
                    : 'bg-gray-600 hover:bg-gray-500'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
