"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx":
/*!*********************************************************!*\
  !*** ./src/components/landing/RoutingVisualization.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoutingVisualization)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,DocumentTextIcon,PencilIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,DocumentTextIcon,PencilIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,DocumentTextIcon,PencilIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,DocumentTextIcon,PencilIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,DocumentTextIcon,PencilIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,DocumentTextIcon,PencilIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,DocumentTextIcon,PencilIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,DocumentTextIcon,PencilIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,DocumentTextIcon,PencilIcon,ShieldCheckIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst routingExamples = [\n    {\n        id: 1,\n        prompt: \"Solve this complex math problem: 2x + 5 = 15\",\n        role: \"logic_reasoning\",\n        roleName: \"Logic & Reasoning\",\n        model: \"GPT-4o\",\n        provider: \"OpenAI\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"text-[#ff6b35]\",\n        bgColor: \"bg-[#ff6b35]/10\",\n        borderColor: \"border-[#ff6b35]/20\",\n        glowColor: \"shadow-[#ff6b35]/50\"\n    },\n    {\n        id: 2,\n        prompt: \"Write a blog post about AI trends\",\n        role: \"writing\",\n        roleName: \"Writing & Content Creation\",\n        model: \"GPT-4o\",\n        provider: \"OpenAI\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"text-[#ff6b35]\",\n        bgColor: \"bg-[#ff6b35]/10\",\n        borderColor: \"border-[#ff6b35]/20\",\n        glowColor: \"shadow-[#ff6b35]/50\"\n    },\n    {\n        id: 3,\n        prompt: \"Build a React component with TypeScript\",\n        role: \"coding_frontend\",\n        roleName: \"Frontend Development\",\n        model: \"Claude 4 Opus\",\n        provider: \"Anthropic\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"text-[#ff6b35]\",\n        bgColor: \"bg-[#ff6b35]/10\",\n        borderColor: \"border-[#ff6b35]/20\",\n        glowColor: \"shadow-[#ff6b35]/50\"\n    },\n    {\n        id: 4,\n        prompt: \"Summarize this research paper\",\n        role: \"research_synthesis\",\n        roleName: \"Research & Analysis\",\n        model: \"DeepSeek R1 0528\",\n        provider: \"DeepSeek\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"text-[#ff6b35]\",\n        bgColor: \"bg-[#ff6b35]/10\",\n        borderColor: \"border-[#ff6b35]/20\",\n        glowColor: \"shadow-[#ff6b35]/50\"\n    }\n];\nfunction RoutingVisualization() {\n    _s();\n    const [activeExample, setActiveExample] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RoutingVisualization.useEffect\": ()=>{\n            const interval = setInterval({\n                \"RoutingVisualization.useEffect.interval\": ()=>{\n                    setIsAnimating(true);\n                    setTimeout({\n                        \"RoutingVisualization.useEffect.interval\": ()=>{\n                            setActiveExample({\n                                \"RoutingVisualization.useEffect.interval\": (prev)=>(prev + 1) % routingExamples.length\n                            }[\"RoutingVisualization.useEffect.interval\"]);\n                            setIsAnimating(false);\n                        }\n                    }[\"RoutingVisualization.useEffect.interval\"], 200);\n                }\n            }[\"RoutingVisualization.useEffect.interval\"], 4000); // Slightly slower for better readability\n            return ({\n                \"RoutingVisualization.useEffect\": ()=>clearInterval(interval)\n            })[\"RoutingVisualization.useEffect\"];\n        }\n    }[\"RoutingVisualization.useEffect\"], []);\n    const currentExample = routingExamples[activeExample];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-99287eaf62199334\" + \" \" + \"relative py-24 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n                },\n                className: \"jsx-99287eaf62199334\" + \" \" + \"absolute inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b200b310d4dd056c\",\n                children: '@-webkit-keyframes flowCurrent{0%{-webkit-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-webkit-transform:translatex(200%);transform:translatex(200%);opacity:0}}@-moz-keyframes flowCurrent{0%{-moz-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-moz-transform:translatex(200%);transform:translatex(200%);opacity:0}}@-o-keyframes flowCurrent{0%{-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-o-transform:translatex(200%);transform:translatex(200%);opacity:0}}@keyframes flowCurrent{0%{-webkit-transform:translatex(-100%);-moz-transform:translatex(-100%);-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-webkit-transform:translatex(200%);-moz-transform:translatex(200%);-o-transform:translatex(200%);transform:translatex(200%);opacity:0}}@-webkit-keyframes pulse-glow{0%,100%{-webkit-box-shadow:0 0 20px rgba(255,107,53,.3);box-shadow:0 0 20px rgba(255,107,53,.3)}50%{-webkit-box-shadow:0 0 40px rgba(255,107,53,.6);box-shadow:0 0 40px rgba(255,107,53,.6)}}@-moz-keyframes pulse-glow{0%,100%{-moz-box-shadow:0 0 20px rgba(255,107,53,.3);box-shadow:0 0 20px rgba(255,107,53,.3)}50%{-moz-box-shadow:0 0 40px rgba(255,107,53,.6);box-shadow:0 0 40px rgba(255,107,53,.6)}}@-o-keyframes pulse-glow{0%,100%{box-shadow:0 0 20px rgba(255,107,53,.3)}50%{box-shadow:0 0 40px rgba(255,107,53,.6)}}@keyframes pulse-glow{0%,100%{-webkit-box-shadow:0 0 20px rgba(255,107,53,.3);-moz-box-shadow:0 0 20px rgba(255,107,53,.3);box-shadow:0 0 20px rgba(255,107,53,.3)}50%{-webkit-box-shadow:0 0 40px rgba(255,107,53,.6);-moz-box-shadow:0 0 40px rgba(255,107,53,.6);box-shadow:0 0 40px rgba(255,107,53,.6)}}@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-10px);transform:translatey(-10px)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px);transform:translatey(0px)}50%{-moz-transform:translatey(-10px);transform:translatey(-10px)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px);transform:translatey(0px)}50%{-o-transform:translatey(-10px);transform:translatey(-10px)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px);-moz-transform:translatey(0px);-o-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-10px);-moz-transform:translatey(-10px);-o-transform:translatey(-10px);transform:translatey(-10px)}}@-webkit-keyframes glossy-highlight{0%{background-position:-200%0}100%{background-position:200%0}}@-moz-keyframes glossy-highlight{0%{background-position:-200%0}100%{background-position:200%0}}@-o-keyframes glossy-highlight{0%{background-position:-200%0}100%{background-position:200%0}}@keyframes glossy-highlight{0%{background-position:-200%0}100%{background-position:200%0}}.current-flow.jsx-99287eaf62199334{-webkit-animation:flowCurrent 3s ease-in-out infinite;-moz-animation:flowCurrent 3s ease-in-out infinite;-o-animation:flowCurrent 3s ease-in-out infinite;animation:flowCurrent 3s ease-in-out infinite}.current-flow-delayed.jsx-99287eaf62199334{-webkit-animation:flowCurrent 3s ease-in-out infinite;-moz-animation:flowCurrent 3s ease-in-out infinite;-o-animation:flowCurrent 3s ease-in-out infinite;animation:flowCurrent 3s ease-in-out infinite;-webkit-animation-delay:1.5s;-moz-animation-delay:1.5s;-o-animation-delay:1.5s;animation-delay:1.5s}.pulse-glow.jsx-99287eaf62199334{-webkit-animation:pulse-glow 2s ease-in-out infinite;-moz-animation:pulse-glow 2s ease-in-out infinite;-o-animation:pulse-glow 2s ease-in-out infinite;animation:pulse-glow 2s ease-in-out infinite}.float-animation.jsx-99287eaf62199334{-webkit-animation:float 6s ease-in-out infinite;-moz-animation:float 6s ease-in-out infinite;-o-animation:float 6s ease-in-out infinite;animation:float 6s ease-in-out infinite}.glossy-card.jsx-99287eaf62199334{position:relative;overflow:hidden}.glossy-card.jsx-99287eaf62199334::before{content:\"\";position:absolute;top:0;left:0;right:0;bottom:0;background:-webkit-linear-gradient(left,transparent,rgba(255,255,255,.2)50%,transparent);background:-moz-linear-gradient(left,transparent,rgba(255,255,255,.2)50%,transparent);background:-o-linear-gradient(left,transparent,rgba(255,255,255,.2)50%,transparent);background:linear-gradient(90deg,transparent,rgba(255,255,255,.2)50%,transparent);-webkit-background-size:200%100%;-moz-background-size:200%100%;-o-background-size:200%100%;background-size:200%100%;opacity:0;-webkit-transition:opacity.3s ease;-moz-transition:opacity.3s ease;-o-transition:opacity.3s ease;transition:opacity.3s ease;pointer-events:none;z-index:10}.glossy-card.jsx-99287eaf62199334:hover::before{opacity:1;-webkit-animation:glossy-highlight 1.5s ease-in-out;-moz-animation:glossy-highlight 1.5s ease-in-out;-o-animation:glossy-highlight 1.5s ease-in-out;animation:glossy-highlight 1.5s ease-in-out}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-99287eaf62199334\" + \" \" + \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-99287eaf62199334\" + \" \" + \"absolute inset-0 opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundImage: \"\\n                linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),\\n                linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)\\n              \",\n                                backgroundSize: '50px 50px'\n                            },\n                            className: \"jsx-99287eaf62199334\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(rgba(255, 107, 53, 0.25) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 107, 53, 0.25) 1px, transparent 1px),\\n              linear-gradient(rgba(0, 0, 0, 0.15) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(0, 0, 0, 0.15) 1px, transparent 1px),\\n              radial-gradient(circle at 25% 25%, rgba(255, 107, 53, 0.4) 2px, transparent 2px),\\n              radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.3) 1px, transparent 1px)\\n            \",\n                            backgroundSize: '80px 80px, 80px 80px, 40px 40px, 40px 40px, 160px 160px, 120px 120px',\n                            backgroundPosition: '0 0, 0 0, 20px 20px, 20px 20px, 0 0, 60px 60px',\n                            mask: \"\\n              radial-gradient(ellipse 90% 90% at center, black 20%, transparent 85%),\\n              linear-gradient(to right, transparent 5%, black 10%, black 90%, transparent 95%),\\n              linear-gradient(to bottom, transparent 5%, black 10%, black 90%, transparent 95%)\\n            \",\n                            maskComposite: 'intersect',\n                            WebkitMask: \"\\n              radial-gradient(ellipse 90% 90% at center, black 20%, transparent 85%),\\n              linear-gradient(to right, transparent 5%, black 10%, black 90%, transparent 95%),\\n              linear-gradient(to bottom, transparent 5%, black 10%, black 90%, transparent 95%)\\n            \",\n                            WebkitMaskComposite: 'source-in'\n                        },\n                        className: \"jsx-99287eaf62199334\" + \" \" + \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-99287eaf62199334\" + \" \" + \"absolute top-1/4 left-1/4 w-96 h-96 bg-[#ff6b35]/8 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-99287eaf62199334\" + \" \" + \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#f7931e]/8 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-99287eaf62199334\" + \" \" + \"absolute top-1/2 right-1/3 w-64 h-64 bg-[#ff6b35]/6 rounded-full blur-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-99287eaf62199334\" + \" \" + \"absolute inset-0 pointer-events-none overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    animation: 'circuit-pulse 4s ease-in-out infinite'\n                                },\n                                className: \"jsx-99287eaf62199334\" + \" \" + \"absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-[#ff6b35]/30 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    animation: 'circuit-pulse 4s ease-in-out infinite',\n                                    animationDelay: '2s'\n                                },\n                                className: \"jsx-99287eaf62199334\" + \" \" + \"absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-[#ff6b35]/30 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    animation: 'circuit-pulse 4s ease-in-out infinite',\n                                    animationDelay: '1s'\n                                },\n                                className: \"jsx-99287eaf62199334\" + \" \" + \"absolute left-1/4 top-0 w-px h-full bg-gradient-to-b from-transparent via-[#ff6b35]/30 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    animation: 'circuit-pulse 4s ease-in-out infinite',\n                                    animationDelay: '3s'\n                                },\n                                className: \"jsx-99287eaf62199334\" + \" \" + \"absolute right-1/4 top-0 w-px h-full bg-gradient-to-b from-transparent via-[#ff6b35]/30 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"82dcaf3221b60e98\",\n                children: \"@-webkit-keyframes circuit-pulse{0%,100%{opacity:.3}50%{opacity:.8}}@-moz-keyframes circuit-pulse{0%,100%{opacity:.3}50%{opacity:.8}}@-o-keyframes circuit-pulse{0%,100%{opacity:.3}50%{opacity:.8}}@keyframes circuit-pulse{0%,100%{opacity:.3}50%{opacity:.8}}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-99287eaf62199334\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-99287eaf62199334\" + \" \" + \"text-center mb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                className: \"text-3xl sm:text-4xl md:text-5xl font-bold mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-99287eaf62199334\" + \" \" + \"text-gray-300\",\n                                        children: \"The fast way to actually\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {\n                                        className: \"jsx-99287eaf62199334\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-99287eaf62199334\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                        children: \"get AI working in your business\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.1\n                                },\n                                className: \"text-lg text-gray-400 max-w-2xl mx-auto\",\n                                children: \"RouKey intelligently routes your requests through our unified gateway to the perfect AI model, eliminating the complexity of managing multiple providers.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-99287eaf62199334\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-99287eaf62199334\" + \" \" + \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6\n                                        },\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                animationDelay: '0s'\n                                            },\n                                            className: \"jsx-99287eaf62199334\" + \" \" + \"bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-[#ff6b35]/30 transition-all duration-300 float-animation\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"flex items-center gap-4 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\" + \" \" + \"w-12 h-12 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center pulse-glow\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"text-xl font-bold text-white\",\n                                                                    children: \"Build multi-step agents\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"text-sm text-gray-400\",\n                                                                    children: \"calling custom tools\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"text-gray-300 mb-6 leading-relaxed\",\n                                                    children: \"Create agentic systems on a single screen. Integrate any LLM into your workflows as fast as you can drag n drop.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\" + \" \" + \"flex items-center gap-3 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"text-gray-300\",\n                                                                    children: \"Update Detected\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\" + \" \" + \"flex items-center gap-3 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.5s'\n                                                                    },\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"text-gray-300\",\n                                                                    children: \"Running Custom Unit Testing\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\" + \" \" + \"flex items-center gap-3 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '1s'\n                                                                    },\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"w-2 h-2 bg-yellow-400 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"text-gray-300\",\n                                                                    children: \"Update Rolled Back Automatically\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"mt-6 px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-medium rounded-lg hover:from-blue-500 hover:to-blue-600 transition-all duration-300 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-99287eaf62199334\",\n                                                            children: \"Explore AI\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.2\n                                        },\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                animationDelay: '2s'\n                                            },\n                                            className: \"jsx-99287eaf62199334\" + \" \" + \"bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-[#ff6b35]/30 transition-all duration-300 float-animation\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"flex items-center gap-4 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\" + \" \" + \"w-12 h-12 bg-gradient-to-br from-purple-600 to-purple-700 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"text-xl font-bold text-white\",\n                                                                    children: \"Self-host everything —\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"text-sm text-gray-400\",\n                                                                    children: \"including AI models\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"text-gray-300 mb-6 leading-relaxed\",\n                                                    children: \"Protect your data by deploying on prem.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\" + \" \" + \"flex items-center gap-3 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"text-gray-300\",\n                                                                    children: \"Deploy with Docker\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\" + \" \" + \"flex items-center gap-3 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"text-gray-300\",\n                                                                    children: \"Access the entire source code on GitHub\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\" + \" \" + \"flex items-center gap-3 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"text-gray-300\",\n                                                                    children: \"Hosted version also available\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.1\n                                },\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        animationDelay: '1s'\n                                    },\n                                    className: \"jsx-99287eaf62199334\" + \" \" + \"bg-gradient-to-br from-gray-900/60 to-gray-800/40 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-[#ff6b35]/30 transition-all duration-300 float-animation\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-99287eaf62199334\" + \" \" + \"flex items-center gap-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"w-12 h-12 bg-gradient-to-br from-green-600 to-green-700 rounded-xl flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-6 h-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-99287eaf62199334\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-99287eaf62199334\" + \" \" + \"text-xl font-bold text-white\",\n                                                        children: \"Chat with your own data\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-99287eaf62199334\" + \" \" + \"text-gray-300 mb-6 leading-relaxed\",\n                                            children: \"Use Slack, Teams, SMS, voice, or our embedded chat interface to get accurate answers from your data, create tasks, and complete workflows.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-99287eaf62199334\" + \" \" + \"space-y-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"bg-gray-800/50 rounded-lg p-4 border border-gray-700/30\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-99287eaf62199334\" + \" \" + \"text-sm text-gray-400 mb-2\",\n                                                        children: \"Who held meetings with SpaceX last week?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"bg-gray-700/30 rounded-lg p-4 border border-gray-600/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\" + \" \" + \"text-sm text-gray-300 mb-3\",\n                                                            children: 'On Wednesday, Joe updated the status to \"won\" in Salesforce after a Zoom call.'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\" + \" \" + \"text-sm text-gray-300\",\n                                                            children: \"On Thursday, Sue provided on-site setup and closed the ServiceNow ticket.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"bg-gray-800/50 rounded-lg p-4 border border-gray-700/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\" + \" \" + \"text-sm text-gray-400\",\n                                                            children: \"Create a task in Asana...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-99287eaf62199334\" + \" \" + \"flex items-center gap-2 mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"w-2 h-2 bg-[#ff6b35] rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"text-xs text-gray-500\",\n                                                                    children: \"AI is typing...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-99287eaf62199334\" + \" \" + \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Ask anything about your data...\",\n                                                    readOnly: true,\n                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"w-full bg-gray-800/50 border border-gray-700/50 rounded-lg px-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:border-[#ff6b35]/50 transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"jsx-99287eaf62199334\" + \" \" + \"absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_DocumentTextIcon_PencilIcon_ShieldCheckIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutingVisualization, \"SlEVdab/r4hG20wt70ej7grkDC0=\");\n_c = RoutingVisualization;\nvar _c;\n$RefreshReg$(_c, \"RoutingVisualization\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx\n"));

/***/ })

});